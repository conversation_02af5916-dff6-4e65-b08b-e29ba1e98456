// Background service worker for Page Marker extension

// Extension installation and update handling
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Page Marker extension installed');
    // Set default settings
    chrome.storage.sync.set({
      defaultColor: '#ff0000',
      defaultSize: 5,
      autoSave: true,
      showWelcome: true
    });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // Inject content script if not already injected
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: togglePageMarker
    });
  } catch (error) {
    console.error('Failed to inject content script:', error);
  }
});

// Function to be injected into the page
function togglePageMarker() {
  // Initialize page marker if not already done
  if (!window.pageMarkerInitialized) {
    window.initPageMarker();
  }

  // Toggle the annotation mode
  if (window.pageMarkerToggle) {
    window.pageMarkerToggle();
  }
}

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'saveAnnotation':
      saveAnnotationData(request.data, sender.tab.url);
      sendResponse({ success: true });
      break;
      
    case 'loadAnnotations':
      loadAnnotationData(sender.tab.url).then(data => {
        sendResponse({ data: data });
      });
      return true; // Keep message channel open for async response
      
    case 'exportAnnotations':
      exportAnnotations(request.data, sender.tab.url, sender.tab.id).then(() => {
        sendResponse({ success: true });
      }).catch((error) => {
        console.error('Export error:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true; // Keep message channel open for async response

    case 'captureWebpage':
      captureWebpageScreenshot(sender.tab.id).then((screenshotDataUrl) => {
        sendResponse({ success: true, screenshot: screenshotDataUrl });
      }).catch((error) => {
        console.error('Screenshot capture error:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true; // Keep message channel open for async response
      
    default:
      sendResponse({ error: 'Unknown action' });
  }
});

// Save annotation data to storage
async function saveAnnotationData(annotationData, url) {
  try {
    const domain = new URL(url).hostname;
    const storageKey = `annotations_${domain}`;
    
    let existingData = await chrome.storage.local.get(storageKey);
    if (!existingData[storageKey]) {
      existingData[storageKey] = [];
    }
    
    existingData[storageKey].push({
      ...annotationData,
      timestamp: Date.now(),
      url: url
    });
    
    await chrome.storage.local.set(existingData);
    console.log('Annotation saved for', domain);
  } catch (error) {
    console.error('Failed to save annotation:', error);
  }
}

// Load annotation data from storage
async function loadAnnotationData(url) {
  try {
    const domain = new URL(url).hostname;
    const storageKey = `annotations_${domain}`;
    
    const data = await chrome.storage.local.get(storageKey);
    return data[storageKey] || [];
  } catch (error) {
    console.error('Failed to load annotations:', error);
    return [];
  }
}

// Capture webpage screenshot using Chrome tabs API
async function captureWebpageScreenshot(tabId) {
  try {
    const screenshotDataUrl = await chrome.tabs.captureVisibleTab(null, {
      format: 'png',
      quality: 100
    });

    console.log('Webpage screenshot captured successfully');
    return screenshotDataUrl;

  } catch (error) {
    console.error('Failed to capture webpage screenshot:', error);
    throw error;
  }
}

// Export composite image (webpage + annotations)
async function exportAnnotations(annotationData, url, tabId) {
  try {
    const domain = new URL(url).hostname;

    // Check if we have composite data or need to create it
    let finalImageDataUrl;

    if (annotationData.compositeData) {
      // Use pre-composed image from content script
      finalImageDataUrl = annotationData.compositeData;
    } else if (annotationData.canvasData) {
      // Use canvas data (annotations only)
      finalImageDataUrl = annotationData.canvasData;
    } else {
      throw new Error('No image data provided for export');
    }

    if (!finalImageDataUrl || !finalImageDataUrl.startsWith('data:image/png')) {
      throw new Error('Invalid image data - PNG format required');
    }

    const downloadId = await chrome.downloads.download({
      url: finalImageDataUrl,
      filename: `page-marker-${domain}-${Date.now()}.png`,
      saveAs: true
    });

    console.log('Composite image exported successfully, download ID:', downloadId);
    return downloadId;

  } catch (error) {
    console.error('Failed to export composite image:', error);
    throw error;
  }
}
