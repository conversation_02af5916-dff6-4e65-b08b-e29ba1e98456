# Privacy Policy for Page Marker

## Introduction
Page Marker is a Chrome extension that allows users to annotate web pages. This privacy policy explains what information Page Marker collects, how it is used, and how it is protected.

## Information Collection and Use
Page Marker collects and stores the following information:

- **Annotation Data**: Drawings, highlights, and other annotations created by users
- **User Preferences**: Settings such as default colors, brush sizes, and other customization options

All data collected by Page Marker is stored locally on your device using Chrome's storage API. No data is transmitted to external servers or third parties.

## Data Storage
- **Local Storage**: All annotations are stored in Chrome's local storage, specific to your browser
- **Domain-Based Organization**: Annotations are saved per website domain to organize your notes
- **No Remote Storage**: We do not store your annotations on any remote servers

## Data Export
Page Marker allows you to export your annotations as PNG images. These exports are initiated by you and saved to your local device. We do not track or monitor these exports.

## Permissions
Page Marker requires certain permissions to function properly:
- **activeTab**: To create and display the annotation canvas on the current webpage
- **storage**: To save your annotations and preferences
- **scripting**: To inject the necessary canvas elements for annotation functionality
- **downloads**: To allow exporting annotations as images
- **tabs**: To capture webpage screenshots when exporting annotations
- **host permissions**: To function on any website you choose to annotate

## Third-Party Services
Page Marker does not use any third-party analytics, tracking, or advertising services.

## Changes to This Privacy Policy
We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.