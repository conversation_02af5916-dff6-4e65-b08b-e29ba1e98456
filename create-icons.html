<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator for Page Marker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .download-btn {
            display: block;
            margin: 5px auto;
            padding: 5px 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Page Marker Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <p>1. Click the "Generate Icons" button below to create placeholder icons</p>
            <p>2. Right-click each icon and "Save image as..." to download</p>
            <p>3. Save them in the "icons" folder with the correct names:</p>
            <ul>
                <li>icon16.png (16x16)</li>
                <li>icon32.png (32x32)</li>
                <li>icon48.png (48x48)</li>
                <li>icon128.png (128x128)</li>
            </ul>
            <p>4. For production, replace these with professionally designed icons</p>
        </div>

        <button onclick="generateIcons()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 20px;">
            Generate Icons
        </button>

        <div id="icon-container"></div>
    </div>

    <script>
        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            const container = document.getElementById('icon-container');
            container.innerHTML = '';

            sizes.forEach(size => {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-preview';

                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                
                const ctx = canvas.getContext('2d');
                
                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#007bff');
                gradient.addColorStop(1, '#0056b3');
                
                // Draw background
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);
                
                // Draw pen icon
                ctx.strokeStyle = 'white';
                ctx.fillStyle = 'white';
                ctx.lineWidth = Math.max(1, size / 16);
                
                const scale = size / 16;
                
                // Draw pen body
                ctx.beginPath();
                ctx.moveTo(4 * scale, 12 * scale);
                ctx.lineTo(12 * scale, 4 * scale);
                ctx.lineTo(14 * scale, 6 * scale);
                ctx.lineTo(6 * scale, 14 * scale);
                ctx.closePath();
                ctx.fill();
                
                // Draw pen tip
                ctx.beginPath();
                ctx.moveTo(2 * scale, 14 * scale);
                ctx.lineTo(4 * scale, 12 * scale);
                ctx.lineTo(6 * scale, 14 * scale);
                ctx.lineTo(4 * scale, 16 * scale);
                ctx.closePath();
                ctx.fill();
                
                // Draw small highlight
                ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.fillRect(5 * scale, 5 * scale, 2 * scale, 6 * scale);

                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                label.style.fontSize = '12px';
                label.style.marginTop = '5px';

                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = `Download icon${size}.png`;
                downloadBtn.onclick = () => downloadIcon(canvas, `icon${size}.png`);

                iconDiv.appendChild(canvas);
                iconDiv.appendChild(label);
                iconDiv.appendChild(downloadBtn);
                container.appendChild(iconDiv);
            });
        }

        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // Generate icons on page load
        window.onload = generateIcons;
    </script>
</body>
</html>
