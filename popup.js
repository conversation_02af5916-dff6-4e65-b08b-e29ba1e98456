// Popup script for Page Marker extension

document.addEventListener('DOMContentLoaded', function() {
  // Get DOM elements
  const toggleMarkerBtn = document.getElementById('toggle-marker');
  const clearPageBtn = document.getElementById('clear-page');
  const defaultColorInput = document.getElementById('default-color');
  const colorLabel = document.querySelector('.color-label');
  const defaultSizeInput = document.getElementById('default-size');
  const sizeValue = document.querySelector('.size-value');
  const autoSaveCheckbox = document.getElementById('auto-save');
  const totalAnnotationsSpan = document.getElementById('total-annotations');
  const currentPageAnnotationsSpan = document.getElementById('current-page-annotations');
  const exportDataBtn = document.getElementById('export-data');
  const importDataBtn = document.getElementById('import-data');
  const clearAllDataBtn = document.getElementById('clear-all-data');
  const importFileInput = document.getElementById('import-file');

  // Load saved settings
  loadSettings();
  loadStatistics();

  // Event listeners
  toggleMarkerBtn.addEventListener('click', toggleMarker);
  clearPageBtn.addEventListener('click', clearCurrentPage);
  defaultColorInput.addEventListener('change', updateDefaultColor);
  defaultSizeInput.addEventListener('input', updateDefaultSize);
  autoSaveCheckbox.addEventListener('change', updateAutoSave);
  exportDataBtn.addEventListener('click', exportAllData);
  importDataBtn.addEventListener('click', () => importFileInput.click());
  importFileInput.addEventListener('change', importData);
  clearAllDataBtn.addEventListener('click', clearAllData);

  // Footer links
  document.getElementById('help-link').addEventListener('click', showHelp);
  document.getElementById('feedback-link').addEventListener('click', showFeedback);
  document.getElementById('about-link').addEventListener('click', showAbout);

  // Load settings from storage
  function loadSettings() {
    chrome.storage.sync.get(['defaultColor', 'defaultSize', 'autoSave'], (result) => {
      if (result.defaultColor) {
        defaultColorInput.value = result.defaultColor;
        colorLabel.textContent = result.defaultColor;
      }
      if (result.defaultSize) {
        defaultSizeInput.value = result.defaultSize;
        sizeValue.textContent = result.defaultSize + 'px';
      }
      if (result.autoSave !== undefined) {
        autoSaveCheckbox.checked = result.autoSave;
      }
    });
  }

  // Load statistics
  function loadStatistics() {
    // Get current tab URL
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const currentUrl = tabs[0].url;
      const domain = new URL(currentUrl).hostname;

      // Get all stored data
      chrome.storage.local.get(null, (data) => {
        let totalAnnotations = 0;
        let currentPageAnnotations = 0;

        Object.keys(data).forEach(key => {
          if (key.startsWith('annotations_')) {
            const annotations = data[key];
            totalAnnotations += annotations.length;
            
            if (key === `annotations_${domain}`) {
              currentPageAnnotations = annotations.length;
            }
          }
        });

        totalAnnotationsSpan.textContent = totalAnnotations;
        currentPageAnnotationsSpan.textContent = currentPageAnnotations;
      });
    });
  }

  // Toggle marker functionality
  function toggleMarker() {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: () => {
          if (window.pageMarkerToggle) {
            window.pageMarkerToggle();
          } else {
            // Initialize if not already done
            if (!window.pageMarkerInitialized) {
              window.initPageMarker();
            }
            window.pageMarkerToggle();
          }
        }
      });
    });

    // Update button text
    const isActive = toggleMarkerBtn.textContent.includes('Start');
    toggleMarkerBtn.innerHTML = isActive ? 
      '<span class="btn-icon">⏹️</span><span class="btn-text">Stop Marking</span>' :
      '<span class="btn-icon">🎨</span><span class="btn-text">Start Marking</span>';
  }

  // Clear current page annotations
  function clearCurrentPage() {
    if (confirm('Are you sure you want to clear all annotations on this page?')) {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const domain = new URL(tabs[0].url).hostname;
        const storageKey = `annotations_${domain}`;
        
        chrome.storage.local.remove(storageKey, () => {
          // Also clear the canvas
          chrome.scripting.executeScript({
            target: { tabId: tabs[0].id },
            function: () => {
              const canvas = document.getElementById('page-marker-canvas');
              if (canvas) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
              }
            }
          });
          
          loadStatistics(); // Refresh stats
          showNotification('Page annotations cleared!');
        });
      });
    }
  }

  // Update default color
  function updateDefaultColor() {
    const color = defaultColorInput.value;
    colorLabel.textContent = color;
    
    chrome.storage.sync.set({ defaultColor: color });
    
    // Update current session
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: (newColor) => {
          if (window.pageMarkerSetColor) {
            window.pageMarkerSetColor(newColor);
          }
        },
        args: [color]
      });
    });
  }

  // Update default size
  function updateDefaultSize() {
    const size = defaultSizeInput.value;
    sizeValue.textContent = size + 'px';
    
    chrome.storage.sync.set({ defaultSize: parseInt(size) });
    
    // Update current session
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      chrome.scripting.executeScript({
        target: { tabId: tabs[0].id },
        function: (newSize) => {
          if (window.pageMarkerSetSize) {
            window.pageMarkerSetSize(newSize);
          }
        },
        args: [parseInt(size)]
      });
    });
  }

  // Update auto-save setting
  function updateAutoSave() {
    const autoSave = autoSaveCheckbox.checked;
    chrome.storage.sync.set({ autoSave: autoSave });
  }

  // Export all data
  function exportAllData() {
    chrome.storage.local.get(null, (data) => {
      const exportData = {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        data: data
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      chrome.downloads.download({
        url: url,
        filename: `page-marker-export-${Date.now()}.json`,
        saveAs: true
      });

      showNotification('Data exported successfully!');
    });
  }

  // Import data
  function importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const importData = JSON.parse(e.target.result);
        
        if (importData.version && importData.data) {
          chrome.storage.local.set(importData.data, () => {
            loadStatistics();
            showNotification('Data imported successfully!');
          });
        } else {
          showNotification('Invalid file format!', 'error');
        }
      } catch (error) {
        showNotification('Error reading file!', 'error');
      }
    };
    reader.readAsText(file);
    
    // Reset file input
    event.target.value = '';
  }

  // Clear all data
  function clearAllData() {
    if (confirm('Are you sure you want to delete ALL annotation data? This cannot be undone!')) {
      chrome.storage.local.clear(() => {
        loadStatistics();
        showNotification('All data cleared!');
      });
    }
  }

  // Show notification
  function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: ${type === 'error' ? '#dc3545' : '#28a745'};
      color: white;
      padding: 10px 15px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  // Footer link handlers
  function showHelp() {
    chrome.tabs.create({
      url: 'https://github.com/your-repo/page-marker/wiki'
    });
  }

  function showFeedback() {
    chrome.tabs.create({
      url: 'https://github.com/your-repo/page-marker/issues'
    });
  }

  function showAbout() {
    alert(`Page Marker v1.0.0

A powerful web annotation tool that allows you to draw, highlight, and mark up any webpage.

Features:
• Drawing tools with customizable colors and sizes
• Text highlighting
• Eraser tool
• Undo/Redo functionality
• Auto-save annotations
• Export/Import data

Developed with ❤️ for better web browsing experience.`);
  }

  // Add CSS for notification animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `;
  document.head.appendChild(style);
});
