// Page Marker Content Script
(function() {
  'use strict';

  // Global state
  let isActive = false;
  let currentTool = 'pen';
  let currentColor = '#ff0000';
  let currentSize = 5;
  let isDrawing = false;
  let canvas, ctx;
  let lastX = 0, lastY = 0;
  let annotations = [];
  let undoStack = [];
  let redoStack = [];

  // Dragging state
  let isDragging = false;
  let dragStartX = 0;
  let dragStartY = 0;
  let panelStartX = 0;
  let panelStartY = 0;

  // Tool-specific settings
  const toolSettings = {
    pen: { defaultSize: 5, opacity: 1.0 },
    highlighter: { defaultSize: 14, opacity: 0.12 }, // Reduced opacity for less intense highlighter
    eraser: { defaultSize: 8, opacity: 1.0 }
  };

  // Color palette
  const colors = [
    '#ff0000', '#00ff00', '#0000ff', '#ffff00',
    '#ff00ff', '#00ffff', '#000000', '#ffffff',
    '#ff8000', '#8000ff', '#0080ff', '#80ff00',
    '#ff0080', '#00ff80', '#808080', '#800000'
  ];

  // Initialize the page marker
  function initPageMarker() {
    if (window.pageMarkerInitialized) {
      console.log('Page Marker already initialized');
      return;
    }

    // Ensure we start in inactive state
    isActive = false;
    window.pageMarkerActive = false;

    // Only create panel and canvas - no toggle button
    createToolPanel();
    createCanvas();
    setupEventListeners();
    setupDragFunctionality();
    loadAnnotations();

    window.pageMarkerInitialized = true;
    window.pageMarkerToggle = toggleAnnotationMode;

    console.log('Page Marker initialized');
  }

  // Setup drag functionality for the panel
  function setupDragFunctionality() {
    const panel = document.getElementById('page-marker-panel');
    if (!panel) return;

    panel.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);

    // Touch events for mobile
    panel.addEventListener('touchstart', startDragTouch);
    document.addEventListener('touchmove', dragTouch);
    document.addEventListener('touchend', stopDrag);
  }

  function startDrag(e) {
    if (e.target.closest('.pm-tool') || e.target.closest('.pm-color-display') || e.target.closest('.pm-size-slider')) {
      return; // Don't drag when interacting with tools
    }

    isDragging = true;
    const panel = document.getElementById('page-marker-panel');
    panel.classList.add('dragging');

    dragStartX = e.clientX;
    dragStartY = e.clientY;

    const rect = panel.getBoundingClientRect();
    panelStartX = rect.left;
    panelStartY = rect.top;

    e.preventDefault();
  }

  function drag(e) {
    if (!isDragging) return;

    const deltaX = e.clientX - dragStartX;
    const deltaY = e.clientY - dragStartY;

    const newX = panelStartX + deltaX;
    const newY = panelStartY + deltaY;

    // Keep panel within viewport bounds
    const panel = document.getElementById('page-marker-panel');
    const maxX = window.innerWidth - panel.offsetWidth;
    const maxY = window.innerHeight - panel.offsetHeight;

    const clampedX = Math.max(0, Math.min(newX, maxX));
    const clampedY = Math.max(0, Math.min(newY, maxY));

    panel.style.left = clampedX + 'px';
    panel.style.top = clampedY + 'px';
  }

  function stopDrag() {
    if (!isDragging) return;

    isDragging = false;
    const panel = document.getElementById('page-marker-panel');
    if (panel) {
      panel.classList.remove('dragging');
    }
  }

  function startDragTouch(e) {
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    startDrag(mouseEvent);
  }

  function dragTouch(e) {
    if (!isDragging) return;
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    drag(mouseEvent);
  }

  // Create tool panel
  function createToolPanel() {
    // Remove existing panel if it exists
    const existingPanel = document.getElementById('page-marker-panel');
    if (existingPanel) {
      existingPanel.remove();
    }

    const panel = document.createElement('div');
    panel.id = 'page-marker-panel';
    panel.className = 'hidden';
    
    panel.innerHTML = `
      <div class="pm-color-section">
        <div class="pm-color-label">COLOR</div>
        <div class="pm-color-display" style="background-color: ${currentColor}"></div>
        <div class="pm-color-picker">
          ${colors.map(color => 
            `<div class="pm-color-option ${color === currentColor ? 'selected' : ''}" 
                  style="background-color: ${color}" data-color="${color}"></div>`
          ).join('')}
        </div>
      </div>
      
      <div class="pm-tools-section">
        <div class="pm-color-label">TOOLS</div>
        <div class="pm-tools-grid">
          <button class="pm-tool active" data-tool="pen" title="Pen">✏️</button>
          <button class="pm-tool" data-tool="highlighter" title="Highlighter">🖍️</button>
          <button class="pm-tool" data-tool="eraser" title="Eraser">🧽</button>
          <button class="pm-tool" data-tool="select" title="Select">👆</button>
          <button class="pm-tool" data-tool="text" title="Text">T</button>
          <button class="pm-tool" data-tool="line" title="Line">📏</button>
          <button class="pm-tool" data-tool="undo" title="Undo">↶</button>
          <button class="pm-tool" data-tool="redo" title="Redo">↷</button>
          <button class="pm-tool" data-tool="clear" title="Clear All">🗑️</button>
          <button class="pm-tool" data-tool="download" title="Download">💾</button>
        </div>
      </div>
      
      <div class="pm-size-section">
        <div class="pm-color-label">SIZE</div>
        <input type="range" class="pm-size-slider" min="1" max="25" value="${currentSize}">
      </div>
    `;
    
    document.body.appendChild(panel);
    setupPanelEvents(panel);
  }

  // Setup panel event listeners
  function setupPanelEvents(panel) {
    // Color picker
    const colorDisplay = panel.querySelector('.pm-color-display');
    const colorPicker = panel.querySelector('.pm-color-picker');
    
    colorDisplay.addEventListener('click', () => {
      colorPicker.classList.toggle('show');
    });
    
    // Color selection
    panel.querySelectorAll('.pm-color-option').forEach(option => {
      option.addEventListener('click', (e) => {
        currentColor = e.target.dataset.color;
        colorDisplay.style.backgroundColor = currentColor;
        
        // Update selected state
        panel.querySelectorAll('.pm-color-option').forEach(opt => opt.classList.remove('selected'));
        e.target.classList.add('selected');
        
        colorPicker.classList.remove('show');
      });
    });
    
    // Tool selection
    panel.querySelectorAll('.pm-tool').forEach(tool => {
      tool.addEventListener('click', (e) => {
        const toolType = e.target.dataset.tool;

        if (['undo', 'redo', 'clear', 'download'].includes(toolType)) {
          handleToolAction(toolType);
        } else {
          currentTool = toolType;

          // Update size based on tool
          if (toolSettings[toolType]) {
            const sizeSlider = panel.querySelector('.pm-size-slider');
            if (currentTool === 'highlighter') {
              currentSize = Math.max(currentSize, toolSettings.highlighter.defaultSize);
              sizeSlider.value = currentSize;
            }
          }

          // Update active state
          panel.querySelectorAll('.pm-tool').forEach(t => t.classList.remove('active'));
          e.target.classList.add('active');

          updateCanvasCursor();
        }
      });
    });
    
    // Size slider
    const sizeSlider = panel.querySelector('.pm-size-slider');
    sizeSlider.addEventListener('input', (e) => {
      currentSize = parseInt(e.target.value);
    });
    
    // Close color picker when clicking outside
    document.addEventListener('click', (e) => {
      if (!colorDisplay.contains(e.target) && !colorPicker.contains(e.target)) {
        colorPicker.classList.remove('show');
      }
    });
  }

  // Create canvas overlay
  function createCanvas() {
    // Remove existing overlay if it exists
    const existingOverlay = document.getElementById('page-marker-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }

    const overlay = document.createElement('div');
    overlay.id = 'page-marker-overlay';
    overlay.style.display = 'none'; // Start hidden

    canvas = document.createElement('canvas');
    canvas.id = 'page-marker-canvas';

    // Set canvas size to viewport
    resizeCanvas();

    overlay.appendChild(canvas);
    document.body.appendChild(overlay);

    ctx = canvas.getContext('2d');
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Handle window resize
    window.addEventListener('resize', resizeCanvas);
  }

  // Resize canvas to match viewport
  function resizeCanvas() {
    if (!canvas) return;
    
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    // Redraw existing annotations
    redrawAnnotations();
  }

  // Setup event listeners
  function setupEventListeners() {
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);
  }

  // Handle touch events
  function handleTouch(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                     e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
  }

  // Drawing functions
  function startDrawing(e) {
    if (currentTool === 'select' || isDragging) return;

    isDrawing = true;
    [lastX, lastY] = [e.clientX, e.clientY];

    if (currentTool === 'eraser') {
      erase(e.clientX, e.clientY);
    } else {
      saveState();
    }
  }

  function draw(e) {
    if (!isDrawing || currentTool === 'select' || isDragging) return;

    if (currentTool === 'eraser') {
      erase(e.clientX, e.clientY);
    } else {
      drawLine(lastX, lastY, e.clientX, e.clientY);
    }

    [lastX, lastY] = [e.clientX, e.clientY];
  }

  function stopDrawing() {
    if (!isDrawing) return;
    isDrawing = false;

    if (currentTool !== 'eraser' && currentTool !== 'select') {
      saveAnnotation();
    }
  }

  // Draw line function
  function drawLine(x1, y1, x2, y2) {
    // Set blend mode and opacity based on tool
    if (currentTool === 'highlighter') {
      ctx.globalCompositeOperation = 'source-over'; // Draw on top, not blend
      ctx.globalAlpha = toolSettings.highlighter.opacity;
      ctx.lineWidth = Math.max(currentSize, toolSettings.highlighter.defaultSize);
      ctx.strokeStyle = currentColor;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.shadowColor = currentColor;
      ctx.shadowBlur = 16; // Soft glow for clean highlighter look
    } else {
      ctx.globalCompositeOperation = 'source-over';
      ctx.globalAlpha = toolSettings.pen.opacity;
      ctx.lineWidth = currentSize;
      ctx.strokeStyle = currentColor;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.shadowBlur = 0;
    }

    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();

    // Reset global alpha and shadow
    ctx.globalAlpha = 1.0;
    ctx.globalCompositeOperation = 'source-over';
    ctx.shadowBlur = 0;
  }

  // Erase function
  function erase(x, y) {
    ctx.globalCompositeOperation = 'destination-out';
    ctx.beginPath();
    ctx.arc(x, y, currentSize * 2, 0, Math.PI * 2);
    ctx.fill();
  }

  // Update canvas cursor
  function updateCanvasCursor() {
    const cursors = {
      pen: 'crosshair',
      highlighter: 'crosshair',
      eraser: 'grab',
      select: 'default',
      text: 'text',
      line: 'crosshair'
    };
    
    canvas.style.cursor = cursors[currentTool] || 'crosshair';
  }

  // Tool actions
  function handleToolAction(action) {
    switch (action) {
      case 'undo':
        undo();
        break;
      case 'redo':
        redo();
        break;
      case 'clear':
        clearCanvas();
        break;
      case 'download':
        downloadAnnotations();
        break;
    }
  }

  // Helper function to safely get canvas data
  function getCanvasDataURL() {
    try {
      if (!canvas) {
        console.warn('Canvas not available for data URL generation');
        return null;
      }
      return canvas.toDataURL();
    } catch (error) {
      console.error('Error generating canvas data URL:', error);
      showNotification('Error saving canvas data', 'error');
      return null;
    }
  }

  // State management
  function saveState() {
    const dataURL = getCanvasDataURL();
    if (dataURL) {
      undoStack.push(dataURL);
      redoStack = []; // Clear redo stack when new action is performed

      if (undoStack.length > 50) {
        undoStack.shift(); // Limit undo stack size
      }
    }
  }

  function undo() {
    if (undoStack.length > 0) {
      const currentState = getCanvasDataURL();
      if (currentState) {
        redoStack.push(currentState);
        const previousState = undoStack.pop();
        restoreCanvas(previousState);
      }
    }
  }

  function redo() {
    if (redoStack.length > 0) {
      const currentState = getCanvasDataURL();
      if (currentState) {
        undoStack.push(currentState);
        const nextState = redoStack.pop();
        restoreCanvas(nextState);
      }
    }
  }

  function restoreCanvas(dataURL) {
    try {
      if (!dataURL || !canvas || !ctx) {
        console.warn('Cannot restore canvas: missing data or canvas not initialized');
        return;
      }

      const img = new Image();
      img.onload = () => {
        try {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0);
          console.log('Canvas restored successfully');
        } catch (error) {
          console.error('Error drawing restored image:', error);
          showNotification('Failed to restore annotations', 'error');
        }
      };

      img.onerror = () => {
        console.error('Failed to load annotation image data');
        showNotification('Failed to load saved annotations', 'error');
      };

      img.src = dataURL;
    } catch (error) {
      console.error('Error in restoreCanvas:', error);
      showNotification('Error restoring annotations', 'error');
    }
  }

  function clearCanvas() {
    if (confirm('Are you sure you want to clear all annotations?')) {
      saveState();
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      annotations = [];
      saveAnnotations();
    }
  }

  // Toggle annotation mode (called from extension icon)
  function toggleAnnotationMode() {
    isActive = !isActive;
    window.pageMarkerActive = isActive;

    const panel = document.getElementById('page-marker-panel');
    const overlay = document.getElementById('page-marker-overlay');

    if (isActive) {
      // Activate annotation mode
      if (panel) {
        panel.classList.remove('hidden');
      }
      if (overlay) {
        overlay.classList.add('active');
        overlay.style.display = 'block';
        overlay.style.pointerEvents = 'auto';
      }
      console.log('Page Marker activated via extension icon');
    } else {
      // Deactivate annotation mode
      if (panel) {
        panel.classList.add('hidden');
      }
      if (overlay) {
        overlay.classList.remove('active');
        overlay.style.display = 'none';
        overlay.style.pointerEvents = 'none';
      }
      // Stop any ongoing drawing
      isDrawing = false;
      isDragging = false;
      console.log('Page Marker deactivated via extension icon');
    }
  }

  // Data persistence
  function saveAnnotation() {
    const canvasData = getCanvasDataURL();
    if (!canvasData) {
      console.warn('Cannot save annotation: canvas data not available');
      return;
    }

    const annotation = {
      type: currentTool,
      color: currentColor,
      size: currentSize,
      data: canvasData,
      timestamp: Date.now()
    };

    annotations.push(annotation);
    saveAnnotations();
  }

  function saveAnnotations() {
    try {
      // Check if canvas exists and has content before saving
      if (!canvas) {
        console.warn('Canvas not available for saving annotations');
        return;
      }

      const canvasData = getCanvasDataURL();
      if (!canvasData) {
        console.warn('Unable to get canvas data for saving');
        return;
      }

      chrome.runtime.sendMessage({
        action: 'saveAnnotation',
        data: {
          annotations: annotations,
          canvasData: canvasData
        }
      }, (response) => {
        // Handle the response and check for errors
        if (chrome.runtime.lastError) {
          console.error('Failed to save annotations:', chrome.runtime.lastError.message);
          showNotification('Failed to save annotations. Please try again.', 'error');
        } else if (response && response.success) {
          console.log('Annotations saved successfully');
          // Optionally show success notification (uncomment if desired)
          // showNotification('Annotations saved', 'success');
        } else {
          console.warn('Save response:', response);
          showNotification('Annotations may not have saved properly', 'warning');
        }
      });
    } catch (error) {
      console.error('Error in saveAnnotations:', error);
      showNotification('Failed to save annotations due to an error', 'error');
    }
  }

  function loadAnnotations() {
    try {
      chrome.runtime.sendMessage({
        action: 'loadAnnotations'
      }, (response) => {
        // Handle Chrome runtime errors
        if (chrome.runtime.lastError) {
          console.error('Failed to load annotations:', chrome.runtime.lastError.message);
          showNotification('Failed to load saved annotations', 'warning');
          return;
        }

        // Process successful response
        if (response && response.data && response.data.length > 0) {
          const latestData = response.data[response.data.length - 1];
          if (latestData.canvasData) {
            restoreCanvas(latestData.canvasData);
          }
          annotations = latestData.annotations || [];
          console.log('Annotations loaded successfully:', annotations.length, 'items');
        } else {
          console.log('No saved annotations found for this page');
          annotations = [];
        }
      });
    } catch (error) {
      console.error('Error in loadAnnotations:', error);
      showNotification('Error loading annotations', 'error');
      annotations = [];
    }
  }

  function redrawAnnotations() {
    // This would redraw annotations after canvas resize
    // For now, we'll just restore from the latest saved state
    if (annotations.length > 0) {
      loadAnnotations();
    }
  }

  function downloadAnnotations() {
    try {
      // Check if there are any annotations to export
      if (!canvas || (!annotations.length && !hasCanvasContent())) {
        showNotification('No annotations to export', 'warning');
        return;
      }

      console.log('Exporting composite screenshot with annotations...', {
        annotationsCount: annotations.length,
        hasCanvas: !!canvas,
        canvasSize: canvas ? `${canvas.width}x${canvas.height}` : 'none'
      });

      // Create composite image with webpage background + annotations
      createCompositeScreenshot();

    } catch (error) {
      console.error('Download annotations error:', error);
      showNotification('Export failed. Please try again.', 'error');
    }
  }

  // Create composite screenshot with webpage background and annotations
  function createCompositeScreenshot() {
    try {
      // Create a temporary canvas for compositing
      const compositeCanvas = document.createElement('canvas');
      const compositeCtx = compositeCanvas.getContext('2d');

      // Set canvas size to match viewport
      compositeCanvas.width = window.innerWidth;
      compositeCanvas.height = window.innerHeight;

      // Step 1: Capture webpage background using html2canvas-like approach
      captureWebpageBackground().then((backgroundImageData) => {
        // Step 2: Draw background on composite canvas
        const backgroundImg = new Image();
        backgroundImg.onload = () => {
          // Draw webpage background
          compositeCtx.drawImage(backgroundImg, 0, 0, compositeCanvas.width, compositeCanvas.height);

          // Step 3: Draw annotations on top
          if (canvas && hasCanvasContent()) {
            compositeCtx.drawImage(canvas, 0, 0);
          }

          // Step 4: Export composite image
          const compositeDataUrl = compositeCanvas.toDataURL('image/png', 1.0);

          // Send composite image to background script for download
          chrome.runtime.sendMessage({
            action: 'exportAnnotations',
            data: {
              compositeData: compositeDataUrl, // Send composite instead of canvas
              url: window.location.href,
              title: document.title
            }
          }, (response) => {
            if (chrome.runtime.lastError) {
              console.error('Export failed:', chrome.runtime.lastError);
              showNotification('Export failed. Please try again.', 'error');
              tryClientSideCompositeDownload(compositeDataUrl);
            } else if (response && response.success) {
              showNotification('Composite screenshot exported successfully!', 'success');
            } else {
              console.error('Export response:', response);
              showNotification('Export may have failed. Check downloads.', 'warning');
              tryClientSideCompositeDownload(compositeDataUrl);
            }
          });
        };

        backgroundImg.onerror = () => {
          console.error('Failed to load background image');
          showNotification('Failed to capture webpage background', 'error');
        };

        backgroundImg.src = backgroundImageData;

      }).catch((error) => {
        console.error('Failed to capture webpage background:', error);
        showNotification('Failed to capture webpage. Exporting annotations only.', 'warning');
        // Fallback to annotations-only export
        fallbackToAnnotationsOnly();
      });

    } catch (error) {
      console.error('Composite screenshot creation failed:', error);
      showNotification('Export failed. Please try again.', 'error');
    }
  }

  // Capture webpage background using Chrome tabs API
  async function captureWebpageBackground() {
    return new Promise((resolve, reject) => {
      try {
        // Request webpage screenshot from background script
        chrome.runtime.sendMessage({
          action: 'captureWebpage'
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Screenshot capture failed:', chrome.runtime.lastError);
            reject(new Error('Screenshot capture failed'));
          } else if (response && response.success && response.screenshot) {
            resolve(response.screenshot);
          } else {
            console.error('Screenshot response error:', response);
            reject(new Error('Invalid screenshot response'));
          }
        });
      } catch (error) {
        console.error('Background capture error:', error);
        reject(error);
      }
    });
  }



  // Fallback to annotations-only export
  function fallbackToAnnotationsOnly() {
    if (!canvas) return;

    const canvasDataUrl = canvas.toDataURL('image/png', 1.0);

    chrome.runtime.sendMessage({
      action: 'exportAnnotations',
      data: {
        canvasData: canvasDataUrl,
        url: window.location.href,
        title: document.title
      }
    }, (response) => {
      if (chrome.runtime.lastError || !response.success) {
        tryClientSideDownload();
      } else {
        showNotification('Annotations exported (background capture failed)', 'warning');
      }
    });
  }

  // Client-side composite download
  function tryClientSideCompositeDownload(compositeDataUrl) {
    try {
      console.log('Attempting client-side composite download...');

      const domain = window.location.hostname;

      // Convert data URL to blob
      fetch(compositeDataUrl)
        .then(res => res.blob())
        .then(blob => {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `page-marker-${domain}-${Date.now()}.png`;
          link.style.display = 'none';

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          setTimeout(() => URL.revokeObjectURL(url), 1000);

          showNotification('Composite screenshot downloaded!', 'success');
        })
        .catch(error => {
          console.error('Client-side composite download failed:', error);
          showNotification('Download failed. Please try again.', 'error');
        });

    } catch (error) {
      console.error('Client-side composite download error:', error);
      showNotification('Download failed. Please try again.', 'error');
    }
  }

  // Check if canvas has any content
  function hasCanvasContent() {
    if (!canvas || !ctx) return false;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Check if any pixel is not transparent
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] !== 0) return true; // Alpha channel not zero
    }
    return false;
  }

  // Show notification to user
  function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `page-marker-notification ${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${getNotificationColor(type)};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      z-index: 1000001;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      animation: slideInRight 0.3s ease;
      max-width: 300px;
      word-wrap: break-word;
    `;

    // Add animation styles
    if (!document.getElementById('page-marker-notification-styles')) {
      const style = document.createElement('style');
      style.id = 'page-marker-notification-styles';
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  function getNotificationColor(type) {
    switch (type) {
      case 'success': return '#28a745';
      case 'error': return '#dc3545';
      case 'warning': return '#ffc107';
      default: return '#007bff';
    }
  }

  // Fallback client-side image download (if service worker fails)
  function tryClientSideDownload() {
    try {
      console.log('Attempting client-side PNG download fallback...');

      if (!canvas) {
        throw new Error('Canvas not available for download');
      }

      const domain = window.location.hostname;

      // Convert canvas to blob
      canvas.toBlob((blob) => {
        if (!blob) {
          showNotification('Failed to create image. Please try again.', 'error');
          return;
        }

        // Create download URL
        const url = URL.createObjectURL(blob);

        // Create temporary download link
        const link = document.createElement('a');
        link.href = url;
        link.download = `page-marker-${domain}-${Date.now()}.png`;
        link.style.display = 'none';

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Cleanup
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 1000);

        showNotification('Annotations image downloaded via fallback!', 'success');
        console.log('Client-side PNG download successful');

      }, 'image/png', 1.0); // High quality PNG

    } catch (error) {
      console.error('Client-side PNG download failed:', error);
      showNotification('Download failed. Please try refreshing the page.', 'error');
    }
  }

  // Keyboard shortcuts
  function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      if (!isActive) return;

      // Ctrl+Z for undo
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
      }

      // Ctrl+Y or Ctrl+Shift+Z for redo
      if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
        e.preventDefault();
        redo();
      }

      // Escape to exit annotation mode
      if (e.key === 'Escape') {
        e.preventDefault();
        if (isActive) {
          toggleAnnotationMode();
        }
      }

      // Number keys for quick tool selection
      const toolKeys = {
        '1': 'pen',
        '2': 'highlighter',
        '3': 'eraser',
        '4': 'select'
      };

      if (toolKeys[e.key]) {
        e.preventDefault();
        currentTool = toolKeys[e.key];
        updateToolSelection();
      }
    });
  }

  // Update tool selection in UI
  function updateToolSelection() {
    const panel = document.getElementById('page-marker-panel');
    if (panel) {
      panel.querySelectorAll('.pm-tool').forEach(tool => {
        tool.classList.remove('active');
        if (tool.dataset.tool === currentTool) {
          tool.classList.add('active');
        }
      });
      updateCanvasCursor();
    }
  }

  // Expose additional functions for popup integration
  window.pageMarkerSetColor = function(color) {
    currentColor = color;
    const colorDisplay = document.querySelector('.pm-color-display');
    if (colorDisplay) {
      colorDisplay.style.backgroundColor = color;
    }
  };

  window.pageMarkerSetSize = function(size) {
    currentSize = size;
    const sizeSlider = document.querySelector('.pm-size-slider');
    if (sizeSlider) {
      sizeSlider.value = size;
    }
  };

  // Cleanup function
  function cleanup() {
    isActive = false;
    isDrawing = false;
    isDragging = false;
    window.pageMarkerActive = false;

    // Remove UI elements
    const panel = document.getElementById('page-marker-panel');
    const overlay = document.getElementById('page-marker-overlay');

    if (panel) panel.remove();
    if (overlay) overlay.remove();

    console.log('Page Marker cleaned up');
  }

  // Handle page navigation
  function handlePageNavigation() {
    // Deactivate when navigating away
    if (isActive) {
      cleanup();
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageMarker);
  } else {
    initPageMarker();
  }

  // Setup keyboard shortcuts
  setupKeyboardShortcuts();

  // Handle page unload
  window.addEventListener('beforeunload', handlePageNavigation);
  window.addEventListener('pagehide', handlePageNavigation);

  // Expose functions globally
  window.initPageMarker = initPageMarker;
  window.pageMarkerToggle = toggleAnnotationMode;
  window.pageMarkerCleanup = cleanup;

})();
