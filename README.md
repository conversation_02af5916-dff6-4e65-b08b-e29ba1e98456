# Page Marker - Web Annotation Chrome Extension

A powerful Chrome extension that allows users to draw, highlight, and annotate any webpage with a comprehensive set of tools.

## 🎨 Features

### Core Annotation Tools
- **Pen Tool** - Draw freehand with customizable colors and sizes
- **Highlighter Tool** - Highlight text with transparency effects
- **Eraser Tool** - Remove annotations with precision
- **Selection Tool** - Navigate without drawing
- **Text Tool** - Add text annotations (planned)
- **Line Tool** - Draw straight lines (planned)

### User Interface
- **Floating Tool Panel** - Clean, modern interface that overlays on web pages
- **Color Palette** - 16 predefined colors with custom color picker
- **Size Control** - Adjustable brush/marker sizes (1-20px)
- **Undo/Redo** - Full history management
- **Auto-save** - Automatically saves annotations per domain

### Data Management
- **Persistent Storage** - Annotations saved per website domain
- **Export/Import** - Backup and restore annotation data
- **Statistics** - Track annotation usage
- **Clear Functions** - Remove annotations per page or globally

## 🚀 Installation

### Development Installation
1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension directory
5. The Page Marker icon should appear in your extensions toolbar

### Production Installation
- Install from Chrome Web Store (coming soon)

## 📖 Usage

### Getting Started
1. Click the Page Marker extension icon in your toolbar
2. Click "Start Marking" in the popup or click the floating toggle button
3. The tool panel will appear on the right side of the page
4. Select your desired tool, color, and size
5. Start drawing or highlighting on the webpage

### Tool Panel Overview
```
┌─────────────┐
│    COLOR    │ ← Current color display
│   [RED BOX] │ ← Click to open color picker
│             │
│    TOOLS    │
│  ✏️  🖍️     │ ← Pen & Highlighter
│  🧽  👆     │ ← Eraser & Select
│  T   📏     │ ← Text & Line
│  ↶   ↷     │ ← Undo & Redo
│  🗑️  💾     │ ← Clear & Download
│             │
│    SIZE     │
│ ●────────○  │ ← Size slider (1-20px)
└─────────────┘
```

### Keyboard Shortcuts (Planned)
- `Ctrl + Z` - Undo
- `Ctrl + Y` - Redo
- `Ctrl + S` - Save annotations
- `Escape` - Exit annotation mode

## 🛠️ Technical Architecture

### File Structure
```
page-marker/
├── manifest.json          # Extension manifest (v3)
├── background.js          # Service worker
├── content.js            # Main content script
├── content.css           # Styles for overlay
├── popup.html            # Extension popup
├── popup.css             # Popup styles
├── popup.js              # Popup functionality
├── icons/                # Extension icons
└── README.md             # Documentation
```

### Core Components

#### 1. Content Script (`content.js`)
- Canvas-based drawing system
- Tool management and state
- Event handling for mouse/touch
- Annotation persistence
- UI overlay management

#### 2. Background Script (`background.js`)
- Data storage management
- Cross-tab communication
- Export/import functionality
- Extension lifecycle management

#### 3. Popup Interface (`popup.html/js/css`)
- Settings management
- Statistics display
- Data export/import
- Quick actions

### Storage Architecture
```javascript
// Chrome Storage Structure
{
  // Sync storage (settings)
  "defaultColor": "#ff0000",
  "defaultSize": 5,
  "autoSave": true,
  
  // Local storage (annotations)
  "annotations_example.com": [
    {
      "type": "pen",
      "color": "#ff0000",
      "size": 5,
      "data": "data:image/png;base64...",
      "timestamp": 1640995200000
    }
  ]
}
```

## 🎯 Development Roadmap

### Phase 1: Core Functionality ✅
- [x] Basic drawing system
- [x] Tool panel UI
- [x] Color and size controls
- [x] Undo/Redo functionality
- [x] Data persistence

### Phase 2: Enhanced Features 🚧
- [ ] Text annotation tool
- [ ] Shape tools (rectangle, circle, arrow)
- [ ] Layer management
- [ ] Keyboard shortcuts
- [ ] Mobile responsiveness

### Phase 3: Advanced Features 📋
- [ ] Collaborative annotations
- [ ] Cloud synchronization
- [ ] PDF annotation support
- [ ] Screenshot integration
- [ ] Annotation sharing

### Phase 4: Polish & Distribution 📋
- [ ] Performance optimization
- [ ] Accessibility improvements
- [ ] Comprehensive testing
- [ ] Chrome Web Store submission
- [ ] User documentation

## 🔧 Development Setup

### Prerequisites
- Chrome browser (latest version)
- Basic knowledge of JavaScript, HTML, CSS
- Text editor or IDE

### Local Development
1. Make changes to the source files
2. Go to `chrome://extensions/`
3. Click the refresh icon on the Page Marker extension
4. Test your changes on any webpage

### Testing
- Test on various websites (different layouts, responsive designs)
- Verify data persistence across browser sessions
- Check performance with large annotations
- Test export/import functionality

## 🤝 Contributing

### Bug Reports
- Use the GitHub Issues tab
- Include browser version and steps to reproduce
- Attach screenshots if applicable

### Feature Requests
- Describe the feature and use case
- Consider implementation complexity
- Check existing issues for duplicates

### Pull Requests
- Fork the repository
- Create a feature branch
- Follow existing code style
- Test thoroughly before submitting

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Chrome Extensions API documentation
- Canvas API for drawing functionality
- Modern web development best practices
- User feedback and feature requests

## 📞 Support

- GitHub Issues: [Report bugs or request features]
- Email: <EMAIL> (planned)
- Documentation: [Wiki page] (planned)

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Compatibility**: Chrome 88+, Manifest V3
