/* Popup Styles for Page Marker Extension */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8f9fa;
  color: #333;
  line-height: 1.4;
}

.popup-container {
  width: 320px;
  max-height: 600px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 20px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
}

.version {
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

/* Main Content */
.popup-main {
  padding: 20px;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 24px;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #007bff;
  color: white;
}

.action-btn.primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.action-btn.secondary:hover {
  background: #e9ecef;
  color: #495057;
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-size: 12px;
}

/* Settings Section */
.settings-section,
.stats-section,
.data-section {
  margin-bottom: 24px;
}

.settings-section h3,
.stats-section h3,
.data-section h3 {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
}

.setting-item label {
  color: #6c757d;
  font-weight: 500;
}

/* Color Input */
.color-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

#default-color {
  width: 30px;
  height: 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.color-label {
  font-size: 11px;
  color: #6c757d;
  font-family: monospace;
}

/* Size Input */
.size-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

#default-size {
  width: 80px;
  height: 4px;
  border-radius: 2px;
  background: #dee2e6;
  outline: none;
  -webkit-appearance: none;
}

#default-size::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
}

.size-value {
  font-size: 11px;
  color: #6c757d;
  min-width: 25px;
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 13px !important;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #dee2e6;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #007bff;
  border-color: #007bff;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Stats Section */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Data Section */
.data-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-btn {
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #495057;
  cursor: pointer;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.data-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.data-btn.danger {
  color: #dc3545;
  border-color: #dc3545;
}

.data-btn.danger:hover {
  background: #f8d7da;
  border-color: #dc3545;
}

/* Footer */
.popup-footer {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.footer-links a {
  color: #6c757d;
  text-decoration: none;
  font-size: 12px;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: #007bff;
}

/* Responsive */
@media (max-width: 350px) {
  .popup-container {
    width: 280px;
  }
  
  .popup-main {
    padding: 16px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
