<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Marker - Annotate Any Web Page</title>
    <style>
        /* Modern CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'Roboto', sans-serif;
            line-height: 1.7;
            color: #1a1a1a;
            background: #fafbfc;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 2rem;
            background: #ffffff;
            min-height: 100vh;
        }

        /* Typography */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .header h1 {
            font-size: 2.25rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.75rem;
            letter-spacing: -0.025em;
        }

        .header p {
            color: #6b7280;
            font-size: 1.125rem;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Content Sections */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .content-section {
            background: #f9fafb;
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .content-section:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .content-section h2 {
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .content-section p {
            color: #6b7280;
            margin-bottom: 1rem;
        }

        .content-section p:last-child {
            margin-bottom: 0;
        }

        /* Highlight Box */
        .highlight-text {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            color: #92400e;
        }

        .highlight-text strong {
            color: #78350f;
        }

        /* Feature List */
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #f3f4f6;
            position: relative;
            padding-left: 2rem;
            color: #374151;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* Use Case Cards */
        .use-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .use-case {
            border: 1px solid #e5e7eb;
            padding: 2rem;
            text-align: left;
            border-radius: 12px;
            background: #ffffff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .use-case:hover {
            border-color: #6366f1;
            background: #f8faff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }

        .use-case h3 {
            margin-bottom: 1rem;
            color: #374151;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .use-case p {
            color: #6b7280;
            font-size: 1rem;
            line-height: 1.6;
        }

        /* CTA Button */
        .cta-button {
            display: inline-block;
            background: #6366f1;
            color: white;
            padding: 0.875rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            margin-top: 1rem;
        }

        .cta-button:hover {
            background: #4f46e5;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        /* Instructions */
        .instructions {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .instructions h3 {
            color: #047857;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .instructions ol {
            color: #065f46;
            padding-left: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        /* Footer */
        .footer {
            text-align: center;
            margin-top: 4rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
            color: #9ca3af;
            font-size: 0.875rem;
        }

        .footer p {
            margin-bottom: 0.25rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .header h1 {
                font-size: 1.875rem;
            }

            .header p {
                font-size: 1rem;
            }

            .content-section {
                padding: 1.5rem;
            }

            .test-areas {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 1.75rem;
            }

            .test-area {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Page Marker</h1>
            <p>Transform any web page into your personal canvas. Draw, highlight, and annotate with ease.</p>
            <a href="#" class="cta-button">Add to Chrome - It's Free</a>
        </div>

        <div class="instructions">
            <h3>Why Page Marker?</h3>
            <p>Whether you're a student taking notes, a professional reviewing documents, or a researcher collecting insights, Page Marker empowers you to interact with web content like never before. Mark up articles, highlight key information, and create visual annotations that persist across your browsing sessions.</p>
        </div>

        <div class="content-grid">
            <div class="content-section">
                <h2>Express Your Ideas Visually</h2>
                <p>Draw directly on web pages with precision tools that feel natural and responsive.</p>
                <p><strong>Perfect for:</strong> Sketching ideas on design articles, marking up documents, creating visual explanations, or simply doodling while you browse.</p>
            </div>

            <div class="content-section">
                <h2>Highlight What Matters</h2>
                <p>Select and highlight important text with customizable colors and transparency.</p>
                <p><strong>Ideal for:</strong> Research, studying, content curation, and marking key information for later reference.</p>
            </div>
        </div>

        <div class="highlight-text">
            <strong>Pro Tip:</strong> Your annotations are automatically saved and will reappear when you revisit the page. Export your work to share with others or backup your annotations.
        </div>

        <div class="content-section">
            <h2>Powerful Features, Simple to Use</h2>
            <ul class="feature-list">
                <li>Draw with customizable pens in any color or size</li>
                <li>Highlight text with transparent, colorful markers</li>
                <li>Erase mistakes with precision eraser tools</li>
                <li>Undo and redo changes instantly</li>
                <li>Annotations automatically save and persist</li>
                <li>Export your work to share or backup</li>
                <li>Fast keyboard shortcuts for power users</li>
                <li>Works seamlessly on any website</li>
                <li>Clean interface that stays out of your way</li>
            </ul>
        </div>

        <div class="use-cases">
            <div class="use-case">
                <h3>📚 Students & Researchers</h3>
                <p>Take visual notes on articles, highlight key concepts, and create study materials directly on web pages. Perfect for research papers, online textbooks, and educational content.</p>
            </div>

            <div class="use-case">
                <h3>💼 Professionals</h3>
                <p>Review documents, mark up designs, and collaborate on web-based content. Ideal for project managers, designers, and anyone who needs to annotate digital materials.</p>
            </div>

            <div class="use-case">
                <h3>🎨 Creative Minds</h3>
                <p>Sketch ideas on inspiration sites, mark up design references, and create visual feedback on creative work. Turn any webpage into your creative workspace.</p>
            </div>

            <div class="use-case">
                <h3>📖 Content Curators</h3>
                <p>Highlight important information, add visual context to articles, and create annotated collections of web content for later reference or sharing.</p>
            </div>
        </div>

        <div class="content-section">
            <h2>How It Works</h2>
            <p><strong>Step 1:</strong> Install Page Marker from the Chrome Web Store with one click.</p>
            <p><strong>Step 2:</strong> Click the Page Marker icon to activate annotation mode on any webpage.</p>
            <p><strong>Step 3:</strong> Choose your tool - pen, highlighter, or eraser - and start creating.</p>
            <p><strong>Step 4:</strong> Your annotations are automatically saved and will appear when you return to the page.</p>
            <p><strong>Step 5:</strong> Export your work or share annotated pages with others effortlessly.</p>
        </div>



        <div class="content-section" style="text-align: center; margin-top: 3rem;">
            <h2>Ready to Transform Your Web Experience?</h2>
            <p>Join thousands of users who are already annotating, highlighting, and creating on the web with Page Marker.</p>
            <a href="#" class="cta-button" style="font-size: 1.125rem; padding: 1rem 2.5rem;">Get Page Marker Free</a>
            <p style="margin-top: 1rem; color: #9ca3af; font-size: 0.875rem;">Compatible with Chrome • No account required • Instant setup</p>
        </div>

        <div class="footer">
            <p>Page Marker - Annotate Any Web Page</p>
            <p>Empowering digital creativity and productivity since 2024</p>
        </div>
    </div>

    <script>
        // Enhanced user experience functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Interactive use case cards
            const useCases = document.querySelectorAll('.use-case');
            useCases.forEach((useCase, index) => {
                useCase.addEventListener('click', function() {
                    // Add visual feedback for user engagement
                    this.style.background = '#f8faff';
                    this.style.borderColor = '#6366f1';

                    setTimeout(() => {
                        this.style.background = '#ffffff';
                        this.style.borderColor = '#e5e7eb';
                    }, 2000);
                });
            });

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add dynamic testimonial or feature highlight
            const dynamicSection = document.createElement('div');
            dynamicSection.className = 'highlight-text';
            dynamicSection.style.marginTop = '2rem';
            dynamicSection.innerHTML = `
                <strong>💡 Did you know?</strong> Page Marker users report 40% faster information processing when reviewing web content with visual annotations and highlights.
            `;

            // Insert before the final CTA section
            const ctaSection = document.querySelector('.content-section[style*="text-align: center"]');
            ctaSection.parentNode.insertBefore(dynamicSection, ctaSection);

            console.log('Page Marker landing page loaded successfully');
        });
    </script>
</body>
</html>
