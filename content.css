/* Page Marker Extension Styles */

/* Main annotation overlay */
#page-marker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 999999;
  background: transparent;
  display: none; /* Hidden by default */
}

#page-marker-overlay.active {
  display: block;
  pointer-events: auto;
}

#page-marker-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  cursor: crosshair;
}

/* Tool panel - Draggable card design */
#page-marker-panel {
  position: fixed;
  top: 50px;
  right: 20px;
  width: 64px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 12px 8px;
  z-index: 1000000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  user-select: none;
  cursor: move;
  border: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition: box-shadow 0.2s ease;
}

#page-marker-panel:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
}

#page-marker-panel.dragging {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25), 0 4px 8px rgba(0, 0, 0, 0.2);
  transform: scale(1.02);
}

#page-marker-panel.hidden {
  display: none;
}

/* Color section */
.pm-color-section {
  margin-bottom: 12px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pm-color-label {
  font-size: 9px;
  font-weight: 600;
  color: #888;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 100%;
}

.pm-color-display {
  width: 48px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: block;
}

.pm-color-display:hover {
  border-color: #999;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.pm-color-picker {
  display: none;
  position: absolute;
  left: -152px;
  top: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 12px;
  grid-template-columns: repeat(4, 1fr);
  gap: 6px;
  width: 140px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.pm-color-picker.show {
  display: grid;
}

.pm-color-option {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pm-color-option:hover {
  border-color: #666;
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.pm-color-option.selected {
  border-color: #333;
  transform: scale(1.05);
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.2);
}

/* Tools section */
.pm-tools-section {
  margin-bottom: 12px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pm-tools-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  justify-items: center;
  align-items: center;
  width: 100%;
  max-width: 48px;
}

.pm-tool {
  width: 22px;
  height: 22px;
  border: none;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
  font-size: 11px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pm-tool:hover {
  background: #e8e8e8;
  border-color: #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pm-tool.active {
  background: #4285f4;
  color: white;
  border-color: #4285f4;
  box-shadow: 0 2px 6px rgba(66, 133, 244, 0.3);
}

.pm-tool svg {
  width: 12px;
  height: 12px;
  fill: currentColor;
}

/* Size section */
.pm-size-section {
  margin-bottom: 8px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pm-size-slider {
  width: 48px;
  height: 4px;
  border-radius: 2px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pm-size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #4285f4;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.pm-size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.pm-size-slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #4285f4;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

/* Toggle button - REMOVED (using extension icon instead) */

/* Highlight overlay for text selection */
.page-marker-highlight {
  background-color: rgba(255, 255, 0, 0.3);
  border-radius: 2px;
  position: relative;
}

.page-marker-highlight.red {
  background-color: rgba(255, 0, 0, 0.3);
}

.page-marker-highlight.blue {
  background-color: rgba(0, 123, 255, 0.3);
}

.page-marker-highlight.green {
  background-color: rgba(40, 167, 69, 0.3);
}

.page-marker-highlight.yellow {
  background-color: rgba(255, 193, 7, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #page-marker-panel {
    width: 60px;
    padding: 10px 6px;
  }

  .pm-tool {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .pm-color-display {
    width: 44px;
    height: 18px;
  }

  .pm-color-picker {
    left: -120px;
    width: 110px;
  }

  .pm-color-option {
    width: 18px;
    height: 18px;
  }

  .pm-size-slider {
    width: 44px;
  }
}
